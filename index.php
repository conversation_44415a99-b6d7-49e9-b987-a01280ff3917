<?php
/**
 * Main Entry Point for OCP Hotel Reservation System
 * Professional hotel booking system for OCP employees
 */

require_once 'config/database.php';
require_once 'php/auth.php';

// Check if user is logged in
$auth = new Auth();
$user = $auth->getCurrentUser();

// Handle logout
if (isset($_GET['logout'])) {
    $auth->logout();
    header('Location: index.php');
    exit;
}

// Redirect admin to dashboard
if ($user && $user['is_admin']) {
    header('Location: php/admin_dashboard.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Discover Morocco's Finest Hotels</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/animations.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-hotel"></i>
                <span>OCP Hotels</span>
            </div>
            
            <div class="nav-menu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#hotels" class="nav-link">Hotels</a>
                <a href="#about" class="nav-link">About</a>
                <a href="#contact" class="nav-link">Contact</a>
                
                <?php if ($user): ?>
                    <div class="user-menu">
                        <span class="welcome-text">Welcome, <?php echo htmlspecialchars($user['first_name']); ?></span>
                        <div class="dropdown">
                            <button class="dropdown-btn">
                                <i class="fas fa-user-circle"></i>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-content">
                                <a href="php/profile.php"><i class="fas fa-user"></i> My Profile</a>
                                <a href="php/my_bookings.php"><i class="fas fa-calendar-check"></i> My Bookings</a>
                                <a href="?logout=1"><i class="fas fa-sign-out-alt"></i> Logout</a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="auth-buttons">
                        <button class="btn btn-outline" onclick="openModal('loginModal')">Login</button>
                        <button class="btn btn-primary" onclick="openModal('registerModal')">Register</button>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-overlay"></div>
        </div>
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">Discover Morocco's Finest Hotels</h1>
                <p class="hero-subtitle">Exclusive rates for OCP employees at premium hotels across Morocco</p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">Partner Hotels</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">15%</span>
                        <span class="stat-label">OCP Discount</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Support</span>
                    </div>
                </div>
            </div>
            
            <!-- Quick Search Form -->
            <div class="search-form-container">
                <form class="search-form" action="php/search_hotels.php" method="GET">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="destination">Destination</label>
                            <select id="destination" name="city_id" required>
                                <option value="">Select City</option>
                                <?php
                                $db = getDB();
                                $cities = $db->fetchAll("SELECT city_id, city_name, region FROM cities WHERE is_active = 1 ORDER BY city_name");
                                foreach ($cities as $city) {
                                    echo "<option value='{$city['city_id']}'>{$city['city_name']}, {$city['region']}</option>";
                                }
                                ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="checkin">Check-in</label>
                            <input type="date" id="checkin" name="check_in" required min="<?php echo date('Y-m-d'); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="checkout">Check-out</label>
                            <input type="date" id="checkout" name="check_out" required min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="guests">Guests</label>
                            <select id="guests" name="guests">
                                <option value="1">1 Adult</option>
                                <option value="2" selected>2 Adults</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-search">
                                <i class="fas fa-search"></i>
                                Search Hotels
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Featured Hotels Section -->
    <section id="hotels" class="featured-hotels">
        <div class="container">
            <div class="section-header">
                <h2>Featured Hotels</h2>
                <p>Discover our most popular destinations with exclusive OCP employee rates</p>
            </div>
            
            <div class="hotels-grid" id="hotelsGrid">
                <!-- Hotels will be loaded dynamically -->
            </div>
            
            <div class="text-center">
                <button class="btn btn-outline" onclick="loadMoreHotels()">View All Hotels</button>
            </div>
        </div>
    </section>

    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Login to Your Account</h3>
                <span class="close" onclick="closeModal('loginModal')">&times;</span>
            </div>
            <form id="loginForm" class="auth-form">
                <div class="form-group">
                    <label for="loginEmail">Email or Username</label>
                    <input type="text" id="loginEmail" name="login" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <input type="password" id="loginPassword" name="password" required>
                </div>
                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember_me">
                        <span class="checkmark"></span>
                        Remember me for 30 days
                    </label>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Login</button>
                <div class="form-footer">
                    <p>Don't have an account? <a href="#" onclick="switchModal('loginModal', 'registerModal')">Register here</a></p>
                </div>
            </form>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="registerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create OCP Account</h3>
                <span class="close" onclick="closeModal('registerModal')">&times;</span>
            </div>
            <form id="registerForm" class="auth-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">First Name</label>
                        <input type="text" id="firstName" name="first_name" required>
                    </div>
                    <div class="form-group">
                        <label for="lastName">Last Name</label>
                        <input type="text" id="lastName" name="last_name" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="employeeId">Employee ID</label>
                    <input type="text" id="employeeId" name="employee_id" required placeholder="OCP Employee ID">
                </div>
                <div class="form-group">
                    <label for="department">Department</label>
                    <input type="text" id="department" name="department" required>
                </div>
                <div class="form-group">
                    <label for="phone">Phone Number</label>
                    <input type="tel" id="phone" name="phone">
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Confirm Password</label>
                    <input type="password" id="confirmPassword" name="confirm_password" required>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Create Account</button>
                <div class="form-footer">
                    <p>Already have an account? <a href="#" onclick="switchModal('registerModal', 'loginModal')">Login here</a></p>
                </div>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>OCP Hotel Reservations</h4>
                    <p>Exclusive hotel booking platform for OCP employees across Morocco.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#hotels">Hotels</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +212 5XX-XXXXXX</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 OCP Hotel Reservations. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/hotels.js"></script>
</body>
</html>
