<?php
/**
 * Authentication System for OCP Hotel Reservation System
 * Handles user login, registration, session management, and remember me functionality
 */

require_once '../config/database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Register a new user
     */
    public function register($userData) {
        try {
            // Validate input data
            $errors = $this->validateRegistrationData($userData);
            if (!empty($errors)) {
                return ['success' => false, 'errors' => $errors];
            }
            
            // Check if username or email already exists
            $existingUser = $this->db->fetchOne(
                "SELECT user_id FROM users WHERE username = ? OR email = ?",
                [$userData['username'], $userData['email']]
            );
            
            if ($existingUser) {
                return ['success' => false, 'errors' => ['Username or email already exists']];
            }
            
            // Check if employee ID already exists
            if (!empty($userData['employee_id'])) {
                $existingEmployee = $this->db->fetchOne(
                    "SELECT user_id FROM users WHERE employee_id = ?",
                    [$userData['employee_id']]
                );
                
                if ($existingEmployee) {
                    return ['success' => false, 'errors' => ['Employee ID already registered']];
                }
            }
            
            // Insert new user
            $sql = "INSERT INTO users (username, email, password, first_name, last_name, phone, employee_id, department) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $userData['username'],
                $userData['email'],
                $userData['password'], // Plain text as requested
                $userData['first_name'],
                $userData['last_name'],
                $userData['phone'] ?? null,
                $userData['employee_id'] ?? null,
                $userData['department'] ?? null
            ];
            
            $this->db->execute($sql, $params);
            $userId = $this->db->lastInsertId();
            
            logActivity("User registered", ['user_id' => $userId, 'username' => $userData['username']]);
            
            return ['success' => true, 'user_id' => $userId];
            
        } catch (Exception $e) {
            logActivity("Registration failed", ['error' => $e->getMessage()]);
            return ['success' => false, 'errors' => ['Registration failed. Please try again.']];
        }
    }
    
    /**
     * Login user
     */
    public function login($login, $password, $rememberMe = false) {
        try {
            // Find user by username or email
            $user = $this->db->fetchOne(
                "SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1",
                [$login, $login]
            );
            
            if (!$user || $user['password'] !== $password) {
                logActivity("Login failed", ['login' => $login, 'reason' => 'invalid_credentials']);
                return ['success' => false, 'error' => 'Invalid username/email or password'];
            }
            
            // Create session
            $this->createSession($user);
            
            // Handle remember me
            if ($rememberMe) {
                $this->setRememberMeToken($user['user_id']);
            }
            
            logActivity("User logged in", ['user_id' => $user['user_id'], 'username' => $user['username']]);
            
            return ['success' => true, 'user' => $user];
            
        } catch (Exception $e) {
            logActivity("Login error", ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => 'Login failed. Please try again.'];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        // Clear remember me token
        if (isset($_SESSION['user_id'])) {
            $this->clearRememberMeToken($_SESSION['user_id']);
        }
        
        // Clear remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
        
        // Destroy session
        session_destroy();
        
        logActivity("User logged out", ['user_id' => $_SESSION['user_id'] ?? null]);
    }
    
    /**
     * Get current logged-in user
     */
    public function getCurrentUser() {
        // Check session first
        if (isset($_SESSION['user_id'])) {
            return $this->getUserById($_SESSION['user_id']);
        }
        
        // Check remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            return $this->getUserByRememberToken($_COOKIE['remember_token']);
        }
        
        return null;
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        return $this->getCurrentUser() !== null;
    }
    
    /**
     * Check if user is admin
     */
    public function isAdmin() {
        $user = $this->getCurrentUser();
        return $user && $user['is_admin'];
    }
    
    /**
     * Create user session
     */
    private function createSession($user) {
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['is_admin'] = $user['is_admin'];
        $_SESSION['login_time'] = time();
    }
    
    /**
     * Set remember me token
     */
    private function setRememberMeToken($userId) {
        $token = generateToken(32);
        
        // Store token in database
        $this->db->execute(
            "UPDATE users SET remember_token = ? WHERE user_id = ?",
            [$token, $userId]
        );
        
        // Set cookie for 30 days
        setcookie('remember_token', $token, time() + REMEMBER_ME_LIFETIME, '/');
    }
    
    /**
     * Clear remember me token
     */
    private function clearRememberMeToken($userId) {
        $this->db->execute(
            "UPDATE users SET remember_token = NULL WHERE user_id = ?",
            [$userId]
        );
    }
    
    /**
     * Get user by ID
     */
    private function getUserById($userId) {
        return $this->db->fetchOne(
            "SELECT * FROM users WHERE user_id = ? AND is_active = 1",
            [$userId]
        );
    }
    
    /**
     * Get user by remember token
     */
    private function getUserByRememberToken($token) {
        $user = $this->db->fetchOne(
            "SELECT * FROM users WHERE remember_token = ? AND is_active = 1",
            [$token]
        );
        
        if ($user) {
            // Create session for remembered user
            $this->createSession($user);
        }
        
        return $user;
    }
    
    /**
     * Validate registration data
     */
    private function validateRegistrationData($data) {
        $errors = [];
        
        // Required fields
        $required = ['username', 'email', 'password', 'first_name', 'last_name'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
            }
        }
        
        // Email validation
        if (!empty($data['email']) && !isValidEmail($data['email'])) {
            $errors[] = 'Invalid email format';
        }
        
        // Password validation
        if (!empty($data['password']) && strlen($data['password']) < 6) {
            $errors[] = 'Password must be at least 6 characters long';
        }
        
        // Confirm password
        if (!empty($data['password']) && !empty($data['confirm_password'])) {
            if ($data['password'] !== $data['confirm_password']) {
                $errors[] = 'Passwords do not match';
            }
        }
        
        // Username validation
        if (!empty($data['username'])) {
            if (strlen($data['username']) < 3) {
                $errors[] = 'Username must be at least 3 characters long';
            }
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
                $errors[] = 'Username can only contain letters, numbers, and underscores';
            }
        }
        
        return $errors;
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $auth = new Auth();
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'login':
            $result = $auth->login(
                $_POST['login'] ?? '',
                $_POST['password'] ?? '',
                isset($_POST['remember_me'])
            );
            echo json_encode($result);
            break;
            
        case 'register':
            $result = $auth->register($_POST);
            echo json_encode($result);
            break;
            
        case 'logout':
            $auth->logout();
            echo json_encode(['success' => true]);
            break;
            
        default:
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
    exit;
}
?>
