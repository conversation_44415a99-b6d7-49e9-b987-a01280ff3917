-- OCP Hotel Reservation System Database Schema
-- Professional hotel booking system for OCP employees

CREATE DATABASE IF NOT EXISTS ocp_hotel_reservation;
USE ocp_hotel_reservation;

-- Users table (OCP employees and admin)
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    employee_id VARCHAR(20) UNIQUE,
    department VARCHAR(100),
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    remember_token VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Cities/Regions in Morocco
CREATE TABLE cities (
    city_id INT PRIMARY KEY AUTO_INCREMENT,
    city_name VARCHAR(100) NOT NULL,
    region VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Hotels table
CREATE TABLE hotels (
    hotel_id INT PRIMARY KEY AUTO_INCREMENT,
    hotel_name VARCHAR(200) NOT NULL,
    description TEXT,
    address TEXT NOT NULL,
    city_id INT NOT NULL,
    star_rating INT CHECK (star_rating BETWEEN 1 AND 5),
    phone VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(200),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    amenities TEXT, -- JSON format for amenities list
    main_image VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (city_id) REFERENCES cities(city_id)
);

-- Hotel Images table
CREATE TABLE hotel_images (
    image_id INT PRIMARY KEY AUTO_INCREMENT,
    hotel_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    image_caption VARCHAR(200),
    is_main BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 0,
    FOREIGN KEY (hotel_id) REFERENCES hotels(hotel_id) ON DELETE CASCADE
);

-- Room Types
CREATE TABLE room_types (
    room_type_id INT PRIMARY KEY AUTO_INCREMENT,
    type_name VARCHAR(100) NOT NULL, -- Standard, Sea View, Pool View, Suite
    description TEXT,
    max_occupancy INT DEFAULT 2,
    amenities TEXT -- JSON format for room amenities
);

-- Rooms table
CREATE TABLE rooms (
    room_id INT PRIMARY KEY AUTO_INCREMENT,
    hotel_id INT NOT NULL,
    room_type_id INT NOT NULL,
    room_number VARCHAR(20) NOT NULL,
    floor_number INT,
    base_price DECIMAL(10, 2) NOT NULL,
    ocp_discount_percent DECIMAL(5, 2) DEFAULT 15.00, -- OCP employee discount
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (hotel_id) REFERENCES hotels(hotel_id) ON DELETE CASCADE,
    FOREIGN KEY (room_type_id) REFERENCES room_types(room_type_id),
    UNIQUE KEY unique_room (hotel_id, room_number)
);

-- Seasonal Pricing table
CREATE TABLE seasonal_pricing (
    pricing_id INT PRIMARY KEY AUTO_INCREMENT,
    room_type_id INT NOT NULL,
    hotel_id INT NOT NULL,
    season_name VARCHAR(100) NOT NULL, -- High Season, Low Season, Peak Season
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    price_multiplier DECIMAL(4, 2) DEFAULT 1.00, -- 1.5 = 50% increase
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (room_type_id) REFERENCES room_types(room_type_id),
    FOREIGN KEY (hotel_id) REFERENCES hotels(hotel_id)
);

-- Bookings table
CREATE TABLE bookings (
    booking_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_reference VARCHAR(20) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    hotel_id INT NOT NULL,
    room_id INT NOT NULL,
    check_in_date DATE NOT NULL,
    check_out_date DATE NOT NULL,
    adults INT DEFAULT 1 CHECK (adults BETWEEN 1 AND 2),
    children INT DEFAULT 0 CHECK (children >= 0),
    total_nights INT NOT NULL,
    base_amount DECIMAL(10, 2) NOT NULL,
    discount_amount DECIMAL(10, 2) DEFAULT 0.00,
    children_charges DECIMAL(10, 2) DEFAULT 0.00,
    total_amount DECIMAL(10, 2) NOT NULL,
    booking_status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    special_requests TEXT,
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (hotel_id) REFERENCES hotels(hotel_id),
    FOREIGN KEY (room_id) REFERENCES rooms(room_id)
);

-- Booking Status History
CREATE TABLE booking_status_history (
    history_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    changed_by INT, -- user_id who made the change
    change_reason TEXT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(user_id)
);

-- System Settings table
CREATE TABLE system_settings (
    setting_id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user
INSERT INTO users (username, email, password, first_name, last_name, is_admin, employee_id) 
VALUES ('admin', '<EMAIL>', 'admin', 'System', 'Administrator', TRUE, 'ADMIN001');

-- Insert room types
INSERT INTO room_types (type_name, description, max_occupancy, amenities) VALUES
('Standard', 'Comfortable standard room with essential amenities', 2, '["Air Conditioning", "TV", "WiFi", "Private Bathroom"]'),
('Sea View', 'Beautiful room with stunning sea views', 2, '["Air Conditioning", "TV", "WiFi", "Private Bathroom", "Sea View", "Balcony"]'),
('Pool View', 'Relaxing room overlooking the hotel pool area', 2, '["Air Conditioning", "TV", "WiFi", "Private Bathroom", "Pool View", "Balcony"]'),
('Suite', 'Luxurious suite with separate living area', 2, '["Air Conditioning", "TV", "WiFi", "Private Bathroom", "Living Area", "Mini Bar", "Balcony"]');

-- Insert major Moroccan cities
INSERT INTO cities (city_name, region) VALUES
('Casablanca', 'Casablanca-Settat'),
('Rabat', 'Rabat-Salé-Kénitra'),
('Marrakech', 'Marrakech-Safi'),
('Fès', 'Fès-Meknès'),
('Tangier', 'Tanger-Tétouan-Al Hoceïma'),
('Agadir', 'Souss-Massa'),
('Meknès', 'Fès-Meknès'),
('Oujda', 'Oriental'),
('Kenitra', 'Rabat-Salé-Kénitra'),
('Tétouan', 'Tanger-Tétouan-Al Hoceïma'),
('Safi', 'Marrakech-Safi'),
('Mohammedia', 'Casablanca-Settat'),
('Khouribga', 'Béni Mellal-Khénifra'),
('El Jadida', 'Casablanca-Settat'),
('Béni Mellal', 'Béni Mellal-Khénifra');

-- Insert system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('child_charge_per_night', '50.00', 'Additional charge per child per night (MAD)'),
('max_booking_days_advance', '365', 'Maximum days in advance for booking'),
('min_booking_days_advance', '1', 'Minimum days in advance for booking'),
('default_ocp_discount', '15.00', 'Default OCP employee discount percentage'),
('booking_reference_prefix', 'OCP', 'Prefix for booking reference numbers'),
('site_name', 'OCP Hotel Reservations', 'Website name'),
('contact_email', '<EMAIL>', 'Contact email for support');
