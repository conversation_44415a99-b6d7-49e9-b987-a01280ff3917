/**
 * Authentication JavaScript for OCP Hotel Reservation System
 * Handles login, registration, and user session management
 */

// Authentication state
let authState = {
    isLoggedIn: false,
    user: null,
    isLoading: false
};

// Initialize authentication
document.addEventListener('DOMContentLoaded', function() {
    setupAuthEventListeners();
    checkAuthStatus();
});

/**
 * Setup authentication event listeners
 */
function setupAuthEventListeners() {
    // Login form submission
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Registration form submission
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegistration);
    }
    
    // Password confirmation validation
    const confirmPasswordField = document.getElementById('confirmPassword');
    if (confirmPasswordField) {
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    }
    
    // Real-time username validation
    const usernameField = document.getElementById('username');
    if (usernameField) {
        usernameField.addEventListener('blur', validateUsername);
    }
    
    // Real-time email validation
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('blur', validateEmail);
    }
}

/**
 * Handle login form submission
 */
async function handleLogin(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // Validate form
    if (!validateLoginForm(form)) {
        return;
    }
    
    // Show loading state
    showLoading(submitBtn);
    authState.isLoading = true;
    
    try {
        // Add action to form data
        formData.append('action', 'login');
        
        const response = await fetch('php/auth.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('Login successful! Welcome back.', 'success');
            
            // Close modal and redirect
            setTimeout(() => {
                closeModal('loginModal');
                
                // Redirect admin users to dashboard
                if (result.user && result.user.is_admin) {
                    window.location.href = 'php/admin_dashboard.php';
                } else {
                    // Reload page to update UI
                    window.location.reload();
                }
            }, 1000);
            
        } else {
            showAlert(result.error || 'Login failed. Please try again.', 'danger');
        }
        
    } catch (error) {
        console.error('Login error:', error);
        showAlert('Network error. Please check your connection and try again.', 'danger');
    } finally {
        hideLoading(submitBtn);
        authState.isLoading = false;
    }
}

/**
 * Handle registration form submission
 */
async function handleRegistration(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // Validate form
    if (!validateRegistrationForm(form)) {
        return;
    }
    
    // Show loading state
    showLoading(submitBtn);
    authState.isLoading = true;
    
    try {
        // Add action to form data
        formData.append('action', 'register');
        
        const response = await fetch('php/auth.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('Registration successful! You can now login with your credentials.', 'success');
            
            // Switch to login modal
            setTimeout(() => {
                switchModal('registerModal', 'loginModal');
                
                // Pre-fill login form with registered email/username
                const loginEmailField = document.getElementById('loginEmail');
                if (loginEmailField) {
                    loginEmailField.value = formData.get('email');
                }
            }, 1000);
            
        } else {
            if (result.errors && Array.isArray(result.errors)) {
                result.errors.forEach(error => {
                    showAlert(error, 'danger');
                });
            } else {
                showAlert(result.error || 'Registration failed. Please try again.', 'danger');
            }
        }
        
    } catch (error) {
        console.error('Registration error:', error);
        showAlert('Network error. Please check your connection and try again.', 'danger');
    } finally {
        hideLoading(submitBtn);
        authState.isLoading = false;
    }
}

/**
 * Validate login form
 */
function validateLoginForm(form) {
    let isValid = true;
    
    const loginField = form.querySelector('input[name="login"]');
    const passwordField = form.querySelector('input[name="password"]');
    
    // Clear previous errors
    clearFormErrors(form);
    
    // Validate login field
    if (!loginField.value.trim()) {
        showFieldError(loginField, 'Email or username is required');
        isValid = false;
    }
    
    // Validate password
    if (!passwordField.value.trim()) {
        showFieldError(passwordField, 'Password is required');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Validate registration form
 */
function validateRegistrationForm(form) {
    let isValid = true;
    
    // Get all form fields
    const fields = {
        firstName: form.querySelector('input[name="first_name"]'),
        lastName: form.querySelector('input[name="last_name"]'),
        email: form.querySelector('input[name="email"]'),
        username: form.querySelector('input[name="username"]'),
        employeeId: form.querySelector('input[name="employee_id"]'),
        department: form.querySelector('input[name="department"]'),
        password: form.querySelector('input[name="password"]'),
        confirmPassword: form.querySelector('input[name="confirm_password"]')
    };
    
    // Clear previous errors
    clearFormErrors(form);
    
    // Validate required fields
    Object.entries(fields).forEach(([key, field]) => {
        if (field && field.hasAttribute('required') && !field.value.trim()) {
            showFieldError(field, `${getFieldLabel(field)} is required`);
            isValid = false;
        }
    });
    
    // Validate email format
    if (fields.email && fields.email.value && !isValidEmail(fields.email.value)) {
        showFieldError(fields.email, 'Please enter a valid email address');
        isValid = false;
    }
    
    // Validate username
    if (fields.username && fields.username.value) {
        const username = fields.username.value.trim();
        if (username.length < 3) {
            showFieldError(fields.username, 'Username must be at least 3 characters long');
            isValid = false;
        } else if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            showFieldError(fields.username, 'Username can only contain letters, numbers, and underscores');
            isValid = false;
        }
    }
    
    // Validate password
    if (fields.password && fields.password.value) {
        const password = fields.password.value;
        if (password.length < 6) {
            showFieldError(fields.password, 'Password must be at least 6 characters long');
            isValid = false;
        }
    }
    
    // Validate password confirmation
    if (fields.password && fields.confirmPassword && 
        fields.password.value && fields.confirmPassword.value) {
        if (fields.password.value !== fields.confirmPassword.value) {
            showFieldError(fields.confirmPassword, 'Passwords do not match');
            isValid = false;
        }
    }
    
    // Validate employee ID format (basic validation)
    if (fields.employeeId && fields.employeeId.value) {
        const employeeId = fields.employeeId.value.trim();
        if (employeeId.length < 3) {
            showFieldError(fields.employeeId, 'Employee ID must be at least 3 characters long');
            isValid = false;
        }
    }
    
    return isValid;
}

/**
 * Real-time password match validation
 */
function validatePasswordMatch() {
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirmPassword');
    
    if (passwordField && confirmPasswordField && 
        passwordField.value && confirmPasswordField.value) {
        
        if (passwordField.value !== confirmPasswordField.value) {
            showFieldError(confirmPasswordField, 'Passwords do not match');
        } else {
            clearFieldError(confirmPasswordField);
        }
    }
}

/**
 * Real-time username validation
 */
async function validateUsername() {
    const usernameField = document.getElementById('username');
    if (!usernameField || !usernameField.value.trim()) return;
    
    const username = usernameField.value.trim();
    
    // Basic format validation
    if (username.length < 3) {
        showFieldError(usernameField, 'Username must be at least 3 characters long');
        return;
    }
    
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        showFieldError(usernameField, 'Username can only contain letters, numbers, and underscores');
        return;
    }
    
    // Clear any existing errors
    clearFieldError(usernameField);
    
    // TODO: Add server-side username availability check
    // This would require a separate endpoint to check username availability
}

/**
 * Real-time email validation
 */
async function validateEmail() {
    const emailField = document.getElementById('email');
    if (!emailField || !emailField.value.trim()) return;
    
    const email = emailField.value.trim();
    
    // Basic format validation
    if (!isValidEmail(email)) {
        showFieldError(emailField, 'Please enter a valid email address');
        return;
    }
    
    // Clear any existing errors
    clearFieldError(emailField);
    
    // TODO: Add server-side email availability check
    // This would require a separate endpoint to check email availability
}

/**
 * Check current authentication status
 */
function checkAuthStatus() {
    // This function would typically make an API call to check if user is logged in
    // For now, we'll rely on server-side session management
    
    // Update UI based on authentication state
    updateAuthUI();
}

/**
 * Update UI based on authentication state
 */
function updateAuthUI() {
    const authButtons = document.querySelector('.auth-buttons');
    const userMenu = document.querySelector('.user-menu');
    
    if (authState.isLoggedIn && authState.user) {
        // Show user menu, hide auth buttons
        if (authButtons) authButtons.style.display = 'none';
        if (userMenu) userMenu.style.display = 'flex';
        
        // Update welcome text
        const welcomeText = document.querySelector('.welcome-text');
        if (welcomeText) {
            welcomeText.textContent = `Welcome, ${authState.user.first_name}`;
        }
    } else {
        // Show auth buttons, hide user menu
        if (authButtons) authButtons.style.display = 'flex';
        if (userMenu) userMenu.style.display = 'none';
    }
}

/**
 * Logout user
 */
async function logout() {
    try {
        const response = await fetch('php/auth.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=logout'
        });
        
        const result = await response.json();
        
        if (result.success) {
            authState.isLoggedIn = false;
            authState.user = null;
            
            showAlert('You have been logged out successfully.', 'info');
            
            // Redirect to home page
            setTimeout(() => {
                window.location.href = 'index.php';
            }, 1000);
        }
        
    } catch (error) {
        console.error('Logout error:', error);
        // Force redirect even if API call fails
        window.location.href = 'index.php?logout=1';
    }
}

/**
 * Show/hide password toggle
 */
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggleBtn = field.nextElementSibling;
    
    if (field.type === 'password') {
        field.type = 'text';
        toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
    } else {
        field.type = 'password';
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
    }
}

// Export functions for global use
window.logout = logout;
window.togglePassword = togglePassword;
