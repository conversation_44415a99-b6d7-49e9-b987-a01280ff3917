/**
 * Main JavaScript for OCP Hotel Reservation System
 * Professional interactions and smooth animations
 */

// Global variables
let currentUser = null;
let isLoading = false;

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    setupAnimations();
    setupDateValidation();
});

/**
 * Initialize the application
 */
function initializeApp() {
    // Add loading class to body
    document.body.classList.add('loading');
    
    // Remove loading class after page loads
    window.addEventListener('load', function() {
        document.body.classList.remove('loading');
        document.body.classList.add('loaded');
        
        // Trigger entrance animations
        animateOnLoad();
    });
    
    // Setup intersection observer for scroll animations
    setupScrollAnimations();
    
    // Load featured hotels
    loadFeaturedHotels();
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Navigation toggle for mobile
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Close modals when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target.id);
        }
    });
    
    // Close modals with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const activeModal = document.querySelector('.modal.active');
            if (activeModal) {
                closeModal(activeModal.id);
            }
        }
    });
    
    // Form validation on input
    document.querySelectorAll('input, select, textarea').forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

/**
 * Setup scroll-based animations
 */
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                
                // Add stagger effect for multiple elements
                const siblings = entry.target.parentElement.children;
                Array.from(siblings).forEach((sibling, index) => {
                    if (sibling.classList.contains('fade-in-on-scroll')) {
                        setTimeout(() => {
                            sibling.classList.add('visible');
                        }, index * 100);
                    }
                });
            }
        });
    }, observerOptions);
    
    // Observe elements with animation classes
    document.querySelectorAll('.fade-in-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

/**
 * Setup date validation for check-in/check-out
 */
function setupDateValidation() {
    const checkinInput = document.getElementById('checkin');
    const checkoutInput = document.getElementById('checkout');
    
    if (checkinInput && checkoutInput) {
        checkinInput.addEventListener('change', function() {
            const checkinDate = new Date(this.value);
            const minCheckout = new Date(checkinDate);
            minCheckout.setDate(minCheckout.getDate() + 1);
            
            checkoutInput.min = minCheckout.toISOString().split('T')[0];
            
            if (checkoutInput.value && new Date(checkoutInput.value) <= checkinDate) {
                checkoutInput.value = minCheckout.toISOString().split('T')[0];
            }
        });
        
        checkoutInput.addEventListener('change', function() {
            const checkoutDate = new Date(this.value);
            const checkinDate = new Date(checkinInput.value);
            
            if (checkoutDate <= checkinDate) {
                showAlert('Check-out date must be after check-in date', 'warning');
                this.value = '';
            }
        });
    }
}

/**
 * Animate elements on page load
 */
function animateOnLoad() {
    // Animate hero content
    const heroTitle = document.querySelector('.hero-title');
    const heroSubtitle = document.querySelector('.hero-subtitle');
    const heroStats = document.querySelectorAll('.stat-item');
    const searchForm = document.querySelector('.search-form-container');
    
    if (heroTitle) {
        setTimeout(() => heroTitle.classList.add('animate-fadeInUp'), 200);
    }
    if (heroSubtitle) {
        setTimeout(() => heroSubtitle.classList.add('animate-fadeInUp'), 400);
    }
    
    heroStats.forEach((stat, index) => {
        setTimeout(() => stat.classList.add('animate-fadeInUp'), 600 + (index * 100));
    });
    
    if (searchForm) {
        setTimeout(() => searchForm.classList.add('animate-fadeInUp'), 800);
    }
}

/**
 * Modal functions
 */
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);
        
        // Focus first input
        const firstInput = modal.querySelector('input');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 300);
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
        
        // Clear form if exists
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
            clearFormErrors(form);
        }
    }
}

function switchModal(fromModalId, toModalId) {
    closeModal(fromModalId);
    setTimeout(() => {
        openModal(toModalId);
    }, 300);
}

/**
 * Form validation functions
 */
function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name;
    let isValid = true;
    let errorMessage = '';
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = `${getFieldLabel(field)} is required`;
    }
    
    // Email validation
    if (field.type === 'email' && value && !isValidEmail(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address';
    }
    
    // Password validation
    if (field.type === 'password' && value && value.length < 6) {
        isValid = false;
        errorMessage = 'Password must be at least 6 characters long';
    }
    
    // Confirm password validation
    if (fieldName === 'confirm_password' && value) {
        const passwordField = field.form.querySelector('input[name="password"]');
        if (passwordField && value !== passwordField.value) {
            isValid = false;
            errorMessage = 'Passwords do not match';
        }
    }
    
    // Phone validation
    if (field.type === 'tel' && value && !isValidPhone(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid phone number';
    }
    
    if (!isValid) {
        showFieldError(field, errorMessage);
    } else {
        clearFieldError(field);
    }
    
    return isValid;
}

function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('error');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
    field.classList.remove('error');
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

function clearFormErrors(form) {
    form.querySelectorAll('.field-error').forEach(error => error.remove());
    form.querySelectorAll('.error').forEach(field => field.classList.remove('error'));
}

function getFieldLabel(field) {
    const label = field.parentNode.querySelector('label');
    return label ? label.textContent.replace('*', '').trim() : field.name;
}

/**
 * Utility functions
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
}

function showAlert(message, type = 'info', duration = 5000) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} animate-slideInDown`;
    alertDiv.textContent = message;
    
    // Add close button
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '&times;';
    closeBtn.className = 'alert-close';
    closeBtn.onclick = () => alertDiv.remove();
    alertDiv.appendChild(closeBtn);
    
    // Insert at top of page
    document.body.insertBefore(alertDiv, document.body.firstChild);
    
    // Auto remove after duration
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.classList.add('animate-slideOutUp');
            setTimeout(() => alertDiv.remove(), 300);
        }
    }, duration);
}

function showLoading(element) {
    if (element) {
        element.classList.add('loading');
        element.disabled = true;
    }
}

function hideLoading(element) {
    if (element) {
        element.classList.remove('loading');
        element.disabled = false;
    }
}

/**
 * Load featured hotels
 */
function loadFeaturedHotels() {
    const hotelsGrid = document.getElementById('hotelsGrid');
    if (!hotelsGrid) return;
    
    // Show loading state
    hotelsGrid.innerHTML = '<div class="spinner"></div>';
    
    // Simulate API call (replace with actual API call)
    setTimeout(() => {
        // This would be replaced with actual hotel data from the server
        const sampleHotels = [
            {
                id: 1,
                name: 'Royal Mansour Casablanca',
                location: 'Casablanca, Casablanca-Settat',
                rating: 5,
                image: 'images/hotels/royal_mansour_casa.jpg',
                originalPrice: 1200,
                discountedPrice: 1020,
                discount: 15
            },
            {
                id: 2,
                name: 'La Mamounia Marrakech',
                location: 'Marrakech, Marrakech-Safi',
                rating: 5,
                image: 'images/hotels/la_mamounia.jpg',
                originalPrice: 1500,
                discountedPrice: 1275,
                discount: 15
            },
            {
                id: 3,
                name: 'Hotel Sahrai Fès',
                location: 'Fès, Fès-Meknès',
                rating: 5,
                image: 'images/hotels/hotel_sahrai.jpg',
                originalPrice: 900,
                discountedPrice: 765,
                discount: 15
            }
        ];
        
        renderHotels(sampleHotels, hotelsGrid);
    }, 1000);
}

function renderHotels(hotels, container) {
    container.innerHTML = '';
    
    hotels.forEach((hotel, index) => {
        const hotelCard = createHotelCard(hotel);
        hotelCard.classList.add('fade-in-on-scroll', `stagger-${index + 1}`);
        container.appendChild(hotelCard);
    });
}

function createHotelCard(hotel) {
    const card = document.createElement('div');
    card.className = 'hotel-card hover-lift';
    card.innerHTML = `
        <div class="hotel-image">
            <img src="${hotel.image}" alt="${hotel.name}" onerror="this.src='images/placeholder-hotel.jpg'">
            <div class="hotel-rating">
                ${'★'.repeat(hotel.rating)}
            </div>
        </div>
        <div class="hotel-info">
            <h3 class="hotel-name">${hotel.name}</h3>
            <div class="hotel-location">
                <i class="fas fa-map-marker-alt"></i>
                ${hotel.location}
            </div>
            <div class="hotel-amenities">
                <span class="amenity-tag">WiFi</span>
                <span class="amenity-tag">Pool</span>
                <span class="amenity-tag">Spa</span>
            </div>
            <div class="hotel-price">
                <div class="price-info">
                    <div class="original-price">${hotel.originalPrice} MAD</div>
                    <div class="discounted-price">${hotel.discountedPrice} MAD</div>
                </div>
                <div class="discount-badge">${hotel.discount}% OFF</div>
            </div>
        </div>
    `;
    
    card.addEventListener('click', () => {
        // Navigate to hotel details or booking page
        window.location.href = `php/hotel_details.php?id=${hotel.id}`;
    });
    
    return card;
}

function loadMoreHotels() {
    window.location.href = 'php/search_hotels.php';
}
