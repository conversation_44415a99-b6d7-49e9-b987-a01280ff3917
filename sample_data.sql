-- Sample Data for OCP Hotel Reservation System
-- This file contains sample hotels, rooms, and test data

USE ocp_hotel_reservation;

-- Insert sample hotels across Morocco
INSERT INTO hotels (hotel_name, description, address, city_id, star_rating, phone, email, amenities, main_image) VALUES
('Royal Mansour Casablanca', 'Luxury 5-star hotel in the heart of Casablanca with world-class amenities and exceptional service.', 'Boulevard de la Corniche, Casablanca', 1, 5, '+212522123456', '<EMAIL>', '["Spa", "Pool", "Fitness Center", "Restaurant", "Bar", "Business Center", "Concierge", "Room Service", "Valet Parking"]', 'royal_mansour_casa.jpg'),

('Hotel La Tour Hassan Rabat', 'Historic 5-star palace hotel overlooking the Bouregreg River with traditional Moroccan architecture.', 'Avenue Chellah, Rabat', 2, 5, '+212537123789', '<EMAIL>', '["Spa", "Pool", "Multiple Restaurants", "Bar", "Tennis Court", "Business Center", "Garden", "Terrace"]', 'la_tour_hassan.jpg'),

('La Mamounia Marrakech', 'Legendary 5-star palace hotel in Marrakech, a symbol of luxury and Moroccan hospitality since 1923.', 'Avenue Bab Jdid, Marrakech', 3, 5, '+212524388600', '<EMAIL>', '["Spa", "Multiple Pools", "Golf Course", "Multiple Restaurants", "Bar", "Fitness Center", "Gardens", "Casino"]', 'la_mamounia.jpg'),

('Hotel Sahrai Fès', 'Contemporary 5-star hotel with panoramic views of the ancient medina of Fès.', 'Bab Lghoul, Dhar El Mehraz, Fès', 4, 5, '+212535940332', '<EMAIL>', '["Spa", "Pool", "Restaurant", "Bar", "Fitness Center", "Terrace", "Library", "Business Center"]', 'hotel_sahrai.jpg'),

('Grand Hotel Villa de France Tangier', 'Historic 4-star hotel with stunning views of the Strait of Gibraltar and Mediterranean Sea.', 'Rue d\'Angleterre, Tangier', 5, 4, '+212539933111', '<EMAIL>', '["Restaurant", "Bar", "Terrace", "Business Center", "Concierge", "Sea View", "Historic Architecture"]', 'villa_de_france.jpg'),

('Sofitel Agadir Royal Bay Resort', 'Luxury 5-star beachfront resort with direct access to Agadir\'s golden beach.', 'Baie des Palmiers, Agadir', 6, 5, '+212528849200', '<EMAIL>', '["Private Beach", "Multiple Pools", "Spa", "Multiple Restaurants", "Bar", "Kids Club", "Water Sports", "Tennis Court"]', 'sofitel_agadir.jpg'),

('Hotel Transatlantique Meknès', 'Charming 4-star hotel in the imperial city of Meknès with traditional Moroccan design.', 'Rue El Meriniyine, Meknès', 7, 4, '+212535525050', '<EMAIL>', '["Pool", "Restaurant", "Bar", "Garden", "Traditional Architecture", "Business Center"]', 'transatlantique_meknes.jpg'),

('Atlas Terminus & Spa Oujda', 'Modern 4-star hotel in Oujda with contemporary amenities and spa facilities.', 'Boulevard Zerktouni, Oujda', 8, 4, '+212536688888', '<EMAIL>', '["Spa", "Pool", "Restaurant", "Bar", "Fitness Center", "Business Center", "Conference Rooms"]', 'atlas_terminus.jpg'),

('Hotel Mamora Kenitra', 'Comfortable 3-star hotel in Kenitra with modern amenities and friendly service.', 'Avenue Hassan II, Kenitra', 9, 3, '+212537371234', '<EMAIL>', '["Restaurant", "Bar", "Business Center", "Free WiFi", "Air Conditioning"]', 'hotel_mamora.jpg'),

('Marina Bay Mohammedia', 'Seaside 4-star hotel in Mohammedia with beautiful ocean views and marina access.', 'Boulevard de la Corniche, Mohammedia', 12, 4, '+212523321111', '<EMAIL>', '["Sea View", "Restaurant", "Bar", "Marina Access", "Business Center", "Terrace"]', 'marina_bay.jpg');

-- Insert rooms for each hotel
-- Royal Mansour Casablanca (hotel_id: 1)
INSERT INTO rooms (hotel_id, room_type_id, room_number, floor_number, base_price, ocp_discount_percent) VALUES
(1, 1, '101', 1, 1200.00, 15.00), (1, 1, '102', 1, 1200.00, 15.00), (1, 1, '103', 1, 1200.00, 15.00),
(1, 2, '201', 2, 1800.00, 15.00), (1, 2, '202', 2, 1800.00, 15.00), (1, 2, '203', 2, 1800.00, 15.00),
(1, 3, '301', 3, 1600.00, 15.00), (1, 3, '302', 3, 1600.00, 15.00),
(1, 4, '401', 4, 3000.00, 15.00), (1, 4, '402', 4, 3000.00, 15.00);

-- Hotel La Tour Hassan Rabat (hotel_id: 2)
INSERT INTO rooms (hotel_id, room_type_id, room_number, floor_number, base_price, ocp_discount_percent) VALUES
(2, 1, '101', 1, 1000.00, 15.00), (2, 1, '102', 1, 1000.00, 15.00), (2, 1, '103', 1, 1000.00, 15.00),
(2, 2, '201', 2, 1500.00, 15.00), (2, 2, '202', 2, 1500.00, 15.00), (2, 2, '203', 2, 1500.00, 15.00),
(2, 3, '301', 3, 1300.00, 15.00), (2, 3, '302', 3, 1300.00, 15.00),
(2, 4, '401', 4, 2500.00, 15.00), (2, 4, '402', 4, 2500.00, 15.00);

-- La Mamounia Marrakech (hotel_id: 3)
INSERT INTO rooms (hotel_id, room_type_id, room_number, floor_number, base_price, ocp_discount_percent) VALUES
(3, 1, '101', 1, 1500.00, 15.00), (3, 1, '102', 1, 1500.00, 15.00), (3, 1, '103', 1, 1500.00, 15.00),
(3, 2, '201', 2, 2200.00, 15.00), (3, 2, '202', 2, 2200.00, 15.00), (3, 2, '203', 2, 2200.00, 15.00),
(3, 3, '301', 3, 2000.00, 15.00), (3, 3, '302', 3, 2000.00, 15.00),
(3, 4, '401', 4, 4000.00, 15.00), (3, 4, '402', 4, 4000.00, 15.00);

-- Hotel Sahrai Fès (hotel_id: 4)
INSERT INTO rooms (hotel_id, room_type_id, room_number, floor_number, base_price, ocp_discount_percent) VALUES
(4, 1, '101', 1, 900.00, 15.00), (4, 1, '102', 1, 900.00, 15.00), (4, 1, '103', 1, 900.00, 15.00),
(4, 2, '201', 2, 1400.00, 15.00), (4, 2, '202', 2, 1400.00, 15.00), (4, 2, '203', 2, 1400.00, 15.00),
(4, 3, '301', 3, 1200.00, 15.00), (4, 3, '302', 3, 1200.00, 15.00),
(4, 4, '401', 4, 2200.00, 15.00), (4, 4, '402', 4, 2200.00, 15.00);

-- Grand Hotel Villa de France Tangier (hotel_id: 5)
INSERT INTO rooms (hotel_id, room_type_id, room_number, floor_number, base_price, ocp_discount_percent) VALUES
(5, 1, '101', 1, 700.00, 15.00), (5, 1, '102', 1, 700.00, 15.00), (5, 1, '103', 1, 700.00, 15.00),
(5, 2, '201', 2, 1100.00, 15.00), (5, 2, '202', 2, 1100.00, 15.00), (5, 2, '203', 2, 1100.00, 15.00),
(5, 3, '301', 3, 950.00, 15.00), (5, 3, '302', 3, 950.00, 15.00),
(5, 4, '401', 4, 1800.00, 15.00), (5, 4, '402', 4, 1800.00, 15.00);

-- Sofitel Agadir Royal Bay Resort (hotel_id: 6)
INSERT INTO rooms (hotel_id, room_type_id, room_number, floor_number, base_price, ocp_discount_percent) VALUES
(6, 1, '101', 1, 1100.00, 15.00), (6, 1, '102', 1, 1100.00, 15.00), (6, 1, '103', 1, 1100.00, 15.00),
(6, 2, '201', 2, 1700.00, 15.00), (6, 2, '202', 2, 1700.00, 15.00), (6, 2, '203', 2, 1700.00, 15.00),
(6, 3, '301', 3, 1500.00, 15.00), (6, 3, '302', 3, 1500.00, 15.00),
(6, 4, '401', 4, 2800.00, 15.00), (6, 4, '402', 4, 2800.00, 15.00);

-- Insert seasonal pricing
INSERT INTO seasonal_pricing (room_type_id, hotel_id, season_name, start_date, end_date, price_multiplier) VALUES
-- High season (Summer)
(1, 1, 'High Season', '2024-06-01', '2024-08-31', 1.3),
(2, 1, 'High Season', '2024-06-01', '2024-08-31', 1.3),
(3, 1, 'High Season', '2024-06-01', '2024-08-31', 1.3),
(4, 1, 'High Season', '2024-06-01', '2024-08-31', 1.3),

-- Peak season (December holidays)
(1, 1, 'Peak Season', '2024-12-20', '2025-01-05', 1.5),
(2, 1, 'Peak Season', '2024-12-20', '2025-01-05', 1.5),
(3, 1, 'Peak Season', '2024-12-20', '2025-01-05', 1.5),
(4, 1, 'Peak Season', '2024-12-20', '2025-01-05', 1.5);

-- Insert sample test user (OCP employee)
INSERT INTO users (username, email, password, first_name, last_name, phone, employee_id, department) 
VALUES ('test_user', '<EMAIL>', 'password123', 'Ahmed', 'Benali', '+212661234567', 'OCP001', 'Engineering');

-- Insert sample booking
INSERT INTO bookings (booking_reference, user_id, hotel_id, room_id, check_in_date, check_out_date, adults, children, total_nights, base_amount, discount_amount, children_charges, total_amount, booking_status) 
VALUES ('OCP240808ABCD', 2, 1, 1, '2024-09-15', '2024-09-17', 2, 0, 2, 2400.00, 360.00, 0.00, 2040.00, 'confirmed');
